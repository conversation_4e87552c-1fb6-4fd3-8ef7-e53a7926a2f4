# app.py

from flask import Flask

app = Flask(__name__)

# --- 配置你的代理信息 ---
# 这些信息应该是外部用户可以连接到的公网IP和端口
# 替换为你的实际VPS公网IP和frp的remotePort
PROXY_IP = "*************"  # 替换成你的VPS公网IP
PROXY_PORT = 2018                 # 替换成你的frp remotePort

@app.route('/proxy_info', methods=['GET'])
def get_proxy_info():
    """
    提供代理IP和端口的API接口
    """
    return f'{PROXY_IP}:{PROXY_PORT} zdce10v1 mewJiWUG'

if __name__ == '__main__':
    # Flask默认监听在5000端口，只允许本地访问
    # 为了让外部访问这个API，我们需要监听0.0.0.0
    # 注意：直接在生产环境中使用 app.run() 不安全，推荐使用Gunicorn/Nginx
    app.run(host='0.0.0.0', port=5001, debug=False)
