FROM python:3.10

WORKDIR /app

# 假设你的requirements.txt与Dockerfile在同一个目录下
# crucial: 将宿主机上的requirements.txt复制到镜像的/app目录下
COPY requirements.txt .

RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/
RUN pip install --upgrade pip
RUN pip install -r requirements.txt # 这里可以直接用相对路径，因为WORKDIR已经是/app

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
# 这里可以添加清理缓存的命令，减少镜像大小
# RUN pip cache purge

COPY . .

EXPOSE 5001

CMD ["python", "app.py"]